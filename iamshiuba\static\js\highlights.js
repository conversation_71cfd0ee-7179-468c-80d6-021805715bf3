/**
 * HighlightManager - Manages the featured video highlights section
 * with improved performance, error handling, and lazy loading
 */
class HighlightManager {
    constructor() {
        this.container = null;
        this.loadingElement = null;
        this.errorElement = null;
        this.highlightElement = null;
        this.data = null;
        this.isLoaded = false;
        this.observer = null;
        this.retryCount = 0;
        this.maxRetries = 3;
        this.retryDelay = 2000; // 2 seconds

        // Initialize when DOM is loaded
        this.init();
    }

    /**
   * Initialize the highlight manager
   */
    init() {
    // Wait for DOM to be fully loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    /**
   * Set up the highlight manager
   */
    setup() {
    // Find the container and template elements
        this.container = document.getElementById('highlightContainer');

        if (!this.container) {
            console.warn(
                'Highlight container not found. Skipping highlight initialization.'
            );
            return;
        }

        // Get references to the template elements
        this.loadingElement = this.container.querySelector('.highlight-loading');
        this.errorElement = this.container.querySelector('.highlight-error');
        this.highlightElement = this.container.querySelector('.highlight-wrapper');

        // Show loading state
        this.showLoading();

        // Set up intersection observer for lazy loading
        this.setupLazyLoading();
    }

    /**
   * Set up lazy loading with Intersection Observer
   */
    setupLazyLoading() {
    // Skip if Intersection Observer is not supported
        if (!('IntersectionObserver' in window)) {
            this.loadData();
            return;
        }

        // Create observer
        this.observer = new IntersectionObserver(
            (entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting && !this.isLoaded) {
                        this.loadData();
                        this.observer.disconnect();
                    }
                });
            },
            {
                rootMargin: '200px', // Load when within 200px of viewport
                threshold: 0.1
            }
        );

        // Start observing
        this.observer.observe(this.container);
    }

    /**
   * Show loading state in container
   */
    showLoading() {
        if (!this.container || !this.loadingElement) return;

        // Hide other elements
        if (this.errorElement) this.errorElement.style.display = 'none';
        if (this.highlightElement) this.highlightElement.style.display = 'none';

        // Show loading element
        this.loadingElement.style.display = 'block';
    }

    /**
   * Show error state in container
   * @param {string} message - Error message to display
   */
    showError(message) {
        if (!this.container || !this.errorElement) return;

        // Hide other elements
        if (this.loadingElement) this.loadingElement.style.display = 'none';
        if (this.highlightElement) this.highlightElement.style.display = 'none';

        // Update error message and show error element
        const errorMessageElement = this.errorElement.querySelector('#error-message');
        if (errorMessageElement) {
            errorMessageElement.textContent = message;
        }

        this.errorElement.style.display = 'block';

        // Add retry button event listener
        const retryBtn = document.getElementById('retry-highlights');
        if (retryBtn) {
            retryBtn.addEventListener('click', () => {
                this.retryCount = 0;
                this.showLoading();
                this.loadData();
            });
        }
    }

    /**
   * Load highlight data from API
   */
    async loadData() {
        try {
            // Fetch data with timeout
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000);

            // Use the new API endpoint instead of static JSON file
            const response = await fetch('/api/playlists/highlight', {
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(
                    `Failed to load highlights: ${response.status} ${response.statusText}`
                );
            }

            this.data = await response.json();

            if (
                !this.data ||
        !this.data.highlight ||
        !this.data.highlight.playlist_id
            ) {
                throw new Error('Invalid highlight data format');
            }

            this.renderHighlight();
            this.isLoaded = true;
        } catch (error) {
            console.error('Error loading highlights:', error);

            // Handle retry logic
            if (
                this.retryCount < this.maxRetries &&
        (error.name === 'AbortError' || error.name === 'TypeError')
            ) {
                this.retryCount++;
                console.log(
                    `Retrying highlight load (${this.retryCount}/${this.maxRetries})...`
                );

                setTimeout(() => this.loadData(), this.retryDelay);
            } else {
                this.showError(
                    'Não foi possível carregar os destaques. Verifique sua conexão.'
                );
            }
        }
    }

    /**
   * Render the highlight in the container
   */
    renderHighlight() {
        if (!this.container || !this.data || !this.highlightElement) return;

        // Use playlist_id instead of playlistId for compatibility with the API
        const { playlist_id, title, url } = this.data.highlight;

        // Update the button URL if available
        const buttonContainer = document.querySelector('.button-container button');
        if (buttonContainer && url) {
            buttonContainer.onclick = function () {
                window.open(url);
            };
        }

        // Hide other elements
        if (this.loadingElement) this.loadingElement.style.display = 'none';
        if (this.errorElement) this.errorElement.style.display = 'none';

        // Update iframe attributes
        const iframe = this.highlightElement.querySelector('iframe');
        if (iframe) {
            iframe.src = `https://www.youtube.com/embed/videoseries?list=${playlist_id}`;
            iframe.title = title || 'Featured Playlist';
        }

        // Show highlight element
        this.highlightElement.style.display = 'block';
    }
}

// Initialize the highlight manager
new HighlightManager();
