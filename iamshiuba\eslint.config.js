import js from '@eslint/js';
import globals from 'globals';

export default [
    js.configs.recommended,
    {
        ignores: [
            'node_modules/**',
            '.venv/**',
            '__pycache__/**',
            'static/dist/**',
            '**/*.pyc',
            'coverage/**',
            '.coverage/**'
        ]
    },
    {
        languageOptions: {
            globals: {
                ...globals.browser,
                ...globals.es2021,
                ...globals.node
            },
            ecmaVersion: 2021,
            sourceType: 'module'
        },
        rules: {
            'no-unused-vars': 'warn',
            'no-console': 'off',
            'prefer-const': 'error',
            'no-var': 'error',
            'semi': ['error', 'always'],
            'quotes': ['error', 'single'],
            'indent': ['error', 4], // Changed to 4 spaces to match existing code
            'comma-dangle': ['error', 'never']
        }
    }
];
