// Modern JavaScript entry point for iamshiuba
import './input.css';

// API utilities
class ApiClient {
    constructor(baseURL = '/api/v1') {
        this.baseURL = baseURL;
        this.token = localStorage.getItem('access_token');
    }

    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        if (this.token) {
            config.headers.Authorization = `Bearer ${this.token}`;
        }

        try {
            const response = await fetch(url, config);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Request failed');
            }

            return data;
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }

    async get(endpoint) {
        return this.request(endpoint);
    }

    async post(endpoint, data) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    async put(endpoint, data) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    async delete(endpoint) {
        return this.request(endpoint, {
            method: 'DELETE'
        });
    }

    setToken(token) {
        this.token = token;
        localStorage.setItem('access_token', token);
    }

    clearToken() {
        this.token = null;
        localStorage.removeItem('access_token');
    }
}

// Authentication utilities
class AuthManager {
    constructor() {
        this.api = new ApiClient('/auth');
        this.mainApi = new ApiClient('/api/v1');
    }

    async login(username, password) {
        try {
            const response = await this.api.post('/login', { username, password });
            
            if (response.access_token) {
                this.mainApi.setToken(response.access_token);
                localStorage.setItem('refresh_token', response.refresh_token);
                return response;
            }
            
            throw new Error('No access token received');
        } catch (error) {
            console.error('Login failed:', error);
            throw error;
        }
    }

    async refreshToken() {
        try {
            const refreshToken = localStorage.getItem('refresh_token');
            if (!refreshToken) {
                throw new Error('No refresh token available');
            }

            const response = await this.api.post('/refresh', { 
                refresh_token: refreshToken 
            });

            if (response.access_token) {
                this.mainApi.setToken(response.access_token);
                return response;
            }

            throw new Error('Token refresh failed');
        } catch (error) {
            console.error('Token refresh failed:', error);
            this.logout();
            throw error;
        }
    }

    async logout() {
        try {
            await this.api.post('/logout');
        } catch (error) {
            console.error('Logout request failed:', error);
        } finally {
            this.mainApi.clearToken();
            localStorage.removeItem('refresh_token');
        }
    }

    isAuthenticated() {
        return !!localStorage.getItem('access_token');
    }
}

// Playlist manager
class PlaylistManager {
    constructor(apiClient) {
        this.api = apiClient;
    }

    async getPlaylists(filters = {}) {
        const params = new URLSearchParams(filters);
        const endpoint = `/playlists${params.toString() ? '?' + params.toString() : ''}`;
        return this.api.get(endpoint);
    }

    async getPlaylistsByPlatform(platform) {
        return this.api.get(`/playlists/platform/${platform}`);
    }

    async getHighlightPlaylist() {
        return this.api.get('/playlists/highlight');
    }

    async createPlaylist(playlistData) {
        return this.api.post('/playlists', playlistData);
    }

    async updatePlaylist(id, playlistData) {
        return this.api.put(`/playlists/${id}`, playlistData);
    }

    async deletePlaylist(id) {
        return this.api.delete(`/playlists/${id}`);
    }
}

// Initialize global instances
window.apiClient = new ApiClient();
window.authManager = new AuthManager();
window.playlistManager = new PlaylistManager(window.apiClient);

// Enhanced UI utilities
class UIManager {
    static showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
            type === 'error' ? 'bg-red-500 text-white' :
                type === 'success' ? 'bg-green-500 text-white' :
                    type === 'warning' ? 'bg-yellow-500 text-black' :
                        'bg-blue-500 text-white'
        }`;
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 5000);
    }

    static async loadPlaylists(container, platform = null) {
        try {
            const response = platform 
                ? await window.playlistManager.getPlaylistsByPlatform(platform)
                : await window.playlistManager.getPlaylists();

            if (response.success && response.data.playlists) {
                this.renderPlaylists(container, response.data.playlists);
            }
        } catch (error) {
            console.error('Failed to load playlists:', error);
            this.showNotification('Failed to load playlists', 'error');
        }
    }

    static renderPlaylists(container, playlists) {
        if (!container) return;

        container.innerHTML = playlists.map(playlist => `
            <div class="playlist-item p-4 border rounded-lg shadow-sm hover:shadow-md transition-shadow">
                <h3 class="font-bold text-lg">${playlist.title}</h3>
                <p class="text-gray-600">${playlist.description || ''}</p>
                <div class="mt-2">
                    <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                        ${playlist.platform_name}
                    </span>
                    ${playlist.is_highlight ? 
        '<span class="inline-block bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded ml-1">Highlight</span>' 
        : ''
}
                </div>
                <a href="${playlist.url}" target="_blank" 
                   class="inline-block mt-2 text-blue-600 hover:text-blue-800">
                    View Playlist →
                </a>
            </div>
        `).join('');
    }
}

// Make UIManager globally available
window.UIManager = UIManager;

// Auto-load playlists on page load
document.addEventListener('DOMContentLoaded', () => {
    const playlistContainer = document.getElementById('playlists-container');
    if (playlistContainer) {
        UIManager.loadPlaylists(playlistContainer);
    }

    // Load highlight playlist
    const highlightContainer = document.getElementById('highlight-playlist');
    if (highlightContainer) {
        window.playlistManager.getHighlightPlaylist()
            .then(response => {
                if (response.success && response.data.highlight) {
                    UIManager.renderPlaylists(highlightContainer, [response.data.highlight]);
                }
            })
            .catch(error => {
                console.error('Failed to load highlight playlist:', error);
            });
    }
});

console.log('iamshiuba modern frontend initialized');
