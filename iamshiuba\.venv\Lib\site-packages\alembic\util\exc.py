from __future__ import annotations

from typing import Any
from typing import List
from typing import <PERSON><PERSON>
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from alembic.autogenerate import RevisionContext


class CommandError(Exception):
    pass


class AutogenerateDiffsDetected(CommandError):
    def __init__(
        self,
        message: str,
        revision_context: RevisionContext,
        diffs: List[Tuple[Any, ...]],
    ) -> None:
        super().__init__(message)
        self.revision_context = revision_context
        self.diffs = diffs
